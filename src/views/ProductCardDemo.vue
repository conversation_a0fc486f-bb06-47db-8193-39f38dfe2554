<template>
  <div class="container mx-auto p-6">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-light-text-primary dark:text-dark-text-primary mb-2">
        Product Card Demo
      </h1>
      <p class="text-light-text-secondary dark:text-dark-text-secondary">
        Beautiful product cards inspired by modern e-commerce design
      </p>
    </div>

    <!-- Featured Products Section -->
    <section class="py-12 bg-light-bg-secondary dark:bg-dark-bg-secondary rounded-xl">
      <div class="container mx-auto px-6">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h2 class="text-2xl md:text-3xl font-bold tracking-tight text-light-text-primary dark:text-dark-text-primary">
              Featured Products
            </h2>
            <p class="text-light-text-muted dark:text-dark-text-muted mt-2">
              Our most popular items chosen by athletes like you
            </p>
          </div>
          <Button variant="outline" class="mt-2 md:mt-0">
            View all products
          </Button>
        </div>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <ProductCard
            v-for="product in products"
            :key="product.id"
            :product="product"
            @click="handleProductClick"
            @add-to-cart="handleAddToCart"
          />
        </div>
      </div>
    </section>

    <!-- Single Product Card Example -->
    <section class="py-12">
      <h2 class="text-2xl font-bold text-light-text-primary dark:text-dark-text-primary mb-6">
        Single Product Card
      </h2>
      <div class="max-w-sm">
        <ProductCard
          :product="products[0]"
          @click="handleProductClick"
          @add-to-cart="handleAddToCart"
        />
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Button } from '@/components/ui'
import ProductCard from '@/components/examples/ProductCard.vue'

interface Product {
  id: string
  name: string
  price: number
  originalPrice?: number
  image: string
  category: string
  rating: number
  inStock: boolean
}

const products = ref<Product[]>([
  {
    id: '1',
    name: 'Pro Running Shoes',
    price: 129.99,
    rating: 4.8,
    image: '/placeholder.svg?height=300&width=300',
    category: 'Footwear',
    inStock: true,
  },
  {
    id: '2',
    name: 'Performance Basketball',
    price: 49.99,
    originalPrice: 69.99,
    rating: 4.7,
    image: '/placeholder.svg?height=300&width=300',
    category: 'Equipment',
    inStock: true,
  },
  {
    id: '3',
    name: 'Compression Shirt',
    price: 34.99,
    rating: 4.5,
    image: '/placeholder.svg?height=300&width=300',
    category: 'Apparel',
    inStock: true,
  },
  {
    id: '4',
    name: 'Fitness Tracker',
    price: 89.99,
    originalPrice: 119.99,
    rating: 4.9,
    image: '/placeholder.svg?height=300&width=300',
    category: 'Accessories',
    inStock: true,
  },
  {
    id: '5',
    name: 'Yoga Mat Premium',
    price: 45.99,
    rating: 4.6,
    image: '/placeholder.svg?height=300&width=300',
    category: 'Equipment',
    inStock: false,
  },
  {
    id: '6',
    name: 'Sports Water Bottle',
    price: 19.99,
    originalPrice: 29.99,
    rating: 4.3,
    image: '/placeholder.svg?height=300&width=300',
    category: 'Accessories',
    inStock: true,
  },
])

const handleProductClick = (product: Product) => {
  console.log('Product clicked:', product)
  // Navigate to product detail page
}

const handleAddToCart = (product: Product) => {
  console.log('Added to cart:', product)
  // Add to cart logic
}
</script>
