<template>
  <Card
    :hoverable="true"
    variant="bordered"
    class="product-card group cursor-pointer overflow-hidden border border-light-border-primary dark:border-dark-border-primary"
    @click="handleClick"
  >
    <!-- Product Image -->
    <div class="relative h-48 w-full overflow-hidden">
      <img
        :src="product.image || '/placeholder.svg'"
        :alt="product.name"
        class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
        @error="handleImageError"
      />

      <!-- Sale Badge -->
      <Badge
        v-if="product.originalPrice && product.originalPrice > product.price"
        variant="danger"
        size="sm"
        class="absolute top-2 right-2"
      >
        Sale
      </Badge>
    </div>

    <!-- Product Info -->
    <div class="p-4">
      <!-- Category -->
      <div :class="categoryClasses">{{ product.category }}</div>

      <!-- Product Name -->
      <h3 :class="nameClasses">{{ product.name }}</h3>

      <!-- Rating -->
      <div class="flex items-center gap-2 mt-2">
        <div class="flex items-center">
          <n-icon class="h-4 w-4 text-light-accent-sport dark:text-dark-accent-sport">
            <Star />
          </n-icon>
          <span :class="ratingClasses">{{ product.rating }}</span>
        </div>
      </div>

      <!-- Price -->
      <div class="flex items-center gap-2 mt-2">
        <span
          v-if="product.originalPrice && product.originalPrice > product.price"
          :class="originalPriceClasses"
        >
          ${{ product.originalPrice }}
        </span>
        <span :class="priceClasses">${{ product.price }}</span>
      </div>
    </div>

    <!-- Footer -->
    <div class="p-4 pt-0">
      <Button
        variant="primary"
        class="w-full"
        :disabled="!product.inStock"
        @click.stop="handleAddToCart"
      >
        {{ product.inStock ? 'Add to Cart' : 'Out of Stock' }}
      </Button>
    </div>
  </Card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NIcon } from 'naive-ui'
import { Star } from '@vicons/ionicons5'
import { Card, Button, Badge } from '@/components/ui'
import { useThemeClasses } from '@/composables/useTheme'

interface Product {
  id: string
  name: string
  price: number
  originalPrice?: number
  image: string
  category: string
  rating: number
  inStock: boolean
}

interface Props {
  product: Product
}

const props = defineProps<Props>()

const emit = defineEmits<{
  click: [product: Product]
  addToCart: [product: Product]
}>()

const { getTextClass } = useThemeClasses()

// Computed
const categoryClasses = computed(() => [
  getTextClass('muted'),
  'text-sm'
].join(' '))

const nameClasses = computed(() => [
  getTextClass('primary'),
  'font-semibold mt-1'
].join(' '))

const ratingClasses = computed(() => [
  getTextClass('primary'),
  'ml-1 text-sm font-medium'
].join(' '))

const priceClasses = computed(() => [
  getTextClass('primary'),
  'font-semibold'
].join(' '))

const originalPriceClasses = computed(() => [
  getTextClass('muted'),
  'text-sm line-through'
].join(' '))

// Methods
const handleClick = () => {
  emit('click', props.product)
}

const handleAddToCart = () => {
  emit('addToCart', props.product)
}

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  // Use a solid color background with the first letter as fallback
  target.src = `data:image/svg+xml;base64,${btoa(`
    <svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
      <rect width="300" height="200" fill="#00C897"/>
      <text x="150" y="110" font-family="Arial, sans-serif" font-size="48" font-weight="bold" text-anchor="middle" fill="white">
        ${props.product.name.charAt(0)}
      </text>
    </svg>
  `)}`
}
</script>


