<template>
  <div class="product-grid-container">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h2 :class="titleClasses">{{ title }}</h2>
        <p v-if="subtitle" :class="subtitleClasses">{{ subtitle }}</p>
      </div>
      
      <div class="flex items-center space-x-4">
        <!-- View Toggle -->
        <div class="flex items-center bg-light-bg-secondary dark:bg-dark-bg-secondary rounded-lg p-1">
          <button
            :class="getViewButtonClass('grid')"
            @click="currentView = 'grid'"
          >
            <n-icon><GridOutline /></n-icon>
          </button>
          <button
            :class="getViewButtonClass('list')"
            @click="currentView = 'list'"
          >
            <n-icon><ListOutline /></n-icon>
          </button>
        </div>

        <!-- Sort Dropdown -->
        <n-select
          v-model:value="sortBy"
          :options="sortOptions"
          placeholder="Sort by"
          class="w-40"
        />
      </div>
    </div>

    <!-- Filters -->
    <div v-if="showFilters" class="filters-section mb-6">
      <Card>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Category Filter -->
          <div>
            <label class="form-label">Category</label>
            <n-select
              v-model:value="filters.category"
              :options="categoryOptions"
              placeholder="All Categories"
              clearable
            />
          </div>

          <!-- Price Range -->
          <div>
            <label class="form-label">Price Range</label>
            <n-slider
              v-model:value="filters.priceRange"
              range
              :min="0"
              :max="1000"
              :step="10"
              :format-tooltip="(value) => `$${value}`"
            />
          </div>

          <!-- Brand Filter -->
          <div>
            <label class="form-label">Brand</label>
            <n-select
              v-model:value="filters.brand"
              :options="brandOptions"
              placeholder="All Brands"
              clearable
            />
          </div>

          <!-- Rating Filter -->
          <div>
            <label class="form-label">Min Rating</label>
            <n-rate
              v-model:value="filters.minRating"
              allow-half
              clearable
            />
          </div>
        </div>
      </Card>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      <div v-for="i in 8" :key="i" class="animate-pulse">
        <div class="bg-light-bg-secondary dark:bg-dark-bg-secondary rounded-lg h-64"></div>
      </div>
    </div>

    <!-- Products Grid -->
    <div v-else-if="currentView === 'grid'" :class="gridClasses">
      <ProductCard
        v-for="product in filteredProducts"
        :key="product.id"
        :product="product"
        @click="handleProductClick"
        @add-to-cart="handleAddToCart"
      />
    </div>

    <!-- Products List -->
    <div v-else class="space-y-4">
      <ProductListItem
        v-for="product in filteredProducts"
        :key="product.id"
        :product="product"
        @click="handleProductClick"
        @add-to-cart="handleAddToCart"
        @toggle-favorite="handleToggleFavorite"
      />
    </div>

    <!-- Empty State -->
    <div v-if="!loading && !filteredProducts.length" class="text-center py-12">
      <n-icon class="w-16 h-16 mx-auto mb-4 text-light-text-muted dark:text-dark-text-muted">
        <SearchOutline />
      </n-icon>
      <h3 :class="emptyTitleClasses">No products found</h3>
      <p :class="emptyTextClasses">Try adjusting your filters or search terms</p>
      <Button variant="primary" class="mt-4" @click="clearFilters">
        Clear Filters
      </Button>
    </div>

    <!-- Pagination -->
    <div v-if="pagination && filteredProducts.length" class="flex justify-center mt-8">
      <n-pagination
        v-model:page="currentPage"
        :page-count="totalPages"
        :page-size="pageSize"
        show-size-picker
        show-quick-jumper
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { NIcon, NSelect, NSlider, NRate, NPagination } from 'naive-ui'
import { GridOutline, ListOutline, SearchOutline } from '@vicons/ionicons5'
import { Card, Button } from '@/components/ui'
import ProductCard from './ProductCard.vue'
import ProductListItem from './ProductListItem.vue'
import { useThemeClasses } from '@/composables/useTheme'

interface Product {
  id: string
  name: string
  price: number
  originalPrice?: number
  image: string
  category: string
  rating: number
  inStock: boolean
}

interface Props {
  products: Product[]
  title?: string
  subtitle?: string
  loading?: boolean
  showFilters?: boolean
  pagination?: boolean
  pageSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Products',
  loading: false,
  showFilters: true,
  pagination: true,
  pageSize: 12
})

const emit = defineEmits<{
  productClick: [product: Product]
  addToCart: [product: Product]
}>()

const { getTextClass } = useThemeClasses()

// State
const currentView = ref<'grid' | 'list'>('grid')
const sortBy = ref('name')
const currentPage = ref(1)

const filters = ref({
  category: null,
  priceRange: [0, 1000],
  brand: null,
  minRating: 0
})

// Computed
const titleClasses = computed(() => [
  getTextClass('primary'),
  'text-2xl font-bold'
].join(' '))

const subtitleClasses = computed(() => [
  getTextClass('secondary'),
  'text-sm mt-1'
].join(' '))

const emptyTitleClasses = computed(() => [
  getTextClass('primary'),
  'text-xl font-semibold'
].join(' '))

const emptyTextClasses = computed(() => [
  getTextClass('muted'),
  'text-sm'
].join(' '))

const gridClasses = computed(() => {
  const cols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
  }
  
  return `grid ${cols[4]} gap-6`
})

const sortOptions = [
  { label: 'Name A-Z', value: 'name' },
  { label: 'Name Z-A', value: 'name-desc' },
  { label: 'Price Low-High', value: 'price' },
  { label: 'Price High-Low', value: 'price-desc' },
  { label: 'Rating', value: 'rating' },
  { label: 'Newest', value: 'newest' }
]

const categoryOptions = computed(() => {
  const categories = [...new Set(props.products.map(p => p.category))]
  return categories.map(cat => ({ label: cat, value: cat }))
})

const brandOptions = computed(() => {
  const brands = [...new Set(props.products.map(p => p.brand))]
  return brands.map(brand => ({ label: brand, value: brand }))
})

const filteredProducts = computed(() => {
  let filtered = [...props.products]

  // Apply filters
  if (filters.value.category) {
    filtered = filtered.filter(p => p.category === filters.value.category)
  }

  if (filters.value.brand) {
    filtered = filtered.filter(p => p.brand === filters.value.brand)
  }

  if (filters.value.minRating > 0) {
    filtered = filtered.filter(p => p.rating >= filters.value.minRating)
  }

  filtered = filtered.filter(p => 
    p.price >= filters.value.priceRange[0] && 
    p.price <= filters.value.priceRange[1]
  )

  // Apply sorting
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'name':
        return a.name.localeCompare(b.name)
      case 'name-desc':
        return b.name.localeCompare(a.name)
      case 'price':
        return a.price - b.price
      case 'price-desc':
        return b.price - a.price
      case 'rating':
        return b.rating - a.rating
      default:
        return 0
    }
  })

  return filtered
})

const totalPages = computed(() => 
  Math.ceil(filteredProducts.value.length / props.pageSize)
)

// Methods
const getViewButtonClass = (view: 'grid' | 'list') => {
  const baseClass = 'p-2 rounded-md transition-colors duration-200'
  const activeClass = 'bg-light-accent-sport dark:bg-dark-accent-sport text-white'
  const inactiveClass = 'text-light-text-muted dark:text-dark-text-muted hover:text-light-text-primary dark:hover:text-dark-text-primary'
  
  return `${baseClass} ${currentView.value === view ? activeClass : inactiveClass}`
}

const clearFilters = () => {
  filters.value = {
    category: null,
    priceRange: [0, 1000],
    brand: null,
    minRating: 0
  }
}

const handleProductClick = (product: Product) => {
  emit('productClick', product)
}

const handleAddToCart = (product: Product) => {
  emit('addToCart', product)
}

// Watch for filter changes to reset pagination
watch(filters, () => {
  currentPage.value = 1
}, { deep: true })
</script>
