<template>
  <div class="product-grid-container">
    <!-- Header -->
    <div class="mb-6">
      <div class="flex flex-col md:flex-row md:items-center justify-between mb-4 gap-4">
        <div>
          <h2 :class="titleClasses">{{ title }}</h2>
          <p v-if="subtitle" :class="subtitleClasses">{{ subtitle }}</p>
        </div>
      </div>

      <!-- Filters & Sort Controls -->
      <div v-if="showFilters" class="flex flex-col lg:flex-row gap-4 p-4 bg-light-bg-secondary dark:bg-dark-bg-secondary rounded-lg">
        <!-- Category Filter -->
        <div class="flex-1">
          <label :class="labelClasses">Danh mục</label>
          <n-select
            v-model:value="filters.category"
            :options="categoryOptions"
            placeholder="Tất cả danh mục"
            clearable
            size="medium"
          />
        </div>

        <!-- Price Range -->
        <div class="flex-1">
          <label :class="labelClasses">Khoảng giá</label>
          <div class="mt-2">
            <n-slider
              v-model:value="filters.priceRange"
              range
              :min="0"
              :max="3000"
              :step="50"
              :format-tooltip="(value) => formatPrice(value)"
            />
            <div class="flex justify-between text-xs text-light-text-muted dark:text-dark-text-muted mt-1">
              <span>{{ formatPrice(filters.priceRange[0]) }}</span>
              <span>{{ formatPrice(filters.priceRange[1]) }}</span>
            </div>
          </div>
        </div>

        <!-- Sort & Clear Controls -->
        <div class="flex items-end gap-3">
          <!-- Sort Dropdown -->
          <div>
            <label :class="labelClasses">Sắp xếp</label>
            <n-select
              v-model:value="sortBy"
              :options="sortOptions"
              size="medium"
              class="w-32"
            >
              <template #arrow>
                <n-icon>
                  <SwapVerticalOutline />
                </n-icon>
              </template>
            </n-select>
          </div>

          <!-- Clear Button -->
          <Button variant="ghost" size="sm" @click="clearFilters">
            <n-icon class="w-4 h-4 mr-1">
              <RefreshOutline />
            </n-icon>
            Xóa bộ lọc
          </Button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" :class="gridClasses">
      <div v-for="i in 8" :key="i" class="animate-pulse">
        <div class="bg-light-bg-secondary dark:bg-dark-bg-secondary rounded-xl h-72 shadow-sm"></div>
      </div>
    </div>

    <!-- Products Grid -->
    <div v-else :class="gridClasses">
      <ProductCard
        v-for="product in paginatedProducts"
        :key="product.id"
        :product="product"
        @click="handleProductClick"
        @add-to-cart="handleAddToCart"
        @toggle-favorite="handleToggleFavorite"
      />
    </div>

    <!-- Empty State -->
    <div v-if="!loading && !filteredProducts.length" class="text-center py-16">
      <div class="max-w-md mx-auto">
        <n-icon class="w-20 h-20 mx-auto mb-6 text-light-text-muted dark:text-dark-text-muted">
          <SearchOutline />
        </n-icon>
        <h3 :class="emptyTitleClasses">Không tìm thấy sản phẩm</h3>
        <p :class="emptyTextClasses">Hãy thử điều chỉnh bộ lọc hoặc từ khóa tìm kiếm của bạn</p>
        <Button variant="primary" class="mt-6" @click="clearFilters">
          <n-icon class="w-4 h-4 mr-2">
            <RefreshOutline />
          </n-icon>
          Xóa bộ lọc
        </Button>
      </div>
    </div>

    <!-- Pagination -->
    <div v-if="pagination && filteredProducts.length" class="flex justify-center mt-12">
      <n-pagination
        v-model:page="currentPage"
        :page-count="totalPages"
        :page-size="pageSize"
        show-size-picker
        show-quick-jumper
        :page-sizes="[8, 12, 16, 24]"
        :page-size-options="[
          { label: '8 / trang', value: 8 },
          { label: '12 / trang', value: 12 },
          { label: '16 / trang', value: 16 },
          { label: '24 / trang', value: 24 }
        ]"
        :prefix="({ itemCount }) => `Tổng ${itemCount} sản phẩm`"
        :suffix="({ startIndex, endIndex, itemCount }) => `${startIndex}-${endIndex} của ${itemCount}`"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { NIcon, NSelect, NSlider, NPagination } from 'naive-ui'
import { SearchOutline, RefreshOutline, SwapVerticalOutline } from '@vicons/ionicons5'
import { Card, Button } from '@/components/ui'
import ProductCard from './ProductCard.vue'
import { useThemeClasses } from '@/composables/useTheme'

interface Product {
  id: string
  name: string
  price: number
  originalPrice?: number
  image: string
  category: string
  rating: number
  reviews?: number
  inStock: boolean
  isFavorite?: boolean
}

interface Props {
  products: Product[]
  title?: string
  subtitle?: string
  loading?: boolean
  showFilters?: boolean
  pagination?: boolean
  pageSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Sản phẩm',
  loading: false,
  showFilters: true,
  pagination: true,
  pageSize: 12
})

const emit = defineEmits<{
  productClick: [product: Product]
  addToCart: [product: Product]
  toggleFavorite: [product: Product]
}>()

const { getTextClass } = useThemeClasses()

// State
const sortBy = ref('name')
const currentPage = ref(1)

const filters = ref({
  category: null,
  priceRange: [0, 3000]
})

// Computed
const titleClasses = computed(() => [
  getTextClass('primary'),
  'text-3xl font-bold'
].join(' '))

const subtitleClasses = computed(() => [
  getTextClass('secondary'),
  'text-base mt-2'
].join(' '))

const labelClasses = computed(() => [
  getTextClass('primary'),
  'block text-sm font-medium mb-2'
].join(' '))

const emptyTitleClasses = computed(() => [
  getTextClass('primary'),
  'text-2xl font-semibold mb-3'
].join(' '))

const emptyTextClasses = computed(() => [
  getTextClass('muted'),
  'text-base'
].join(' '))

const gridClasses = computed(() => {
  const cols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
  }

  return `grid ${cols[4]} gap-6`
})

const sortOptions = [
  { label: 'Tên A-Z', value: 'name' },
  { label: 'Tên Z-A', value: 'name-desc' },
  { label: 'Giá thấp → cao', value: 'price' },
  { label: 'Giá cao → thấp', value: 'price-desc' },
  { label: 'Đánh giá cao', value: 'rating' },
  { label: 'Mới nhất', value: 'newest' }
]

const categoryOptions = computed(() => {
  const categories = [...new Set(props.products.map(p => p.category))]
  return categories.map(cat => ({ label: cat, value: cat }))
})

const brandOptions = computed(() => {
  // Since we don't have brand in Product interface, return empty array for now
  return []
})

const filteredProducts = computed(() => {
  let filtered = [...props.products]

  // Apply filters
  if (filters.value.category) {
    filtered = filtered.filter(p => p.category === filters.value.category)
  }

  // Price range filter
  filtered = filtered.filter(p =>
    p.price >= filters.value.priceRange[0] &&
    p.price <= filters.value.priceRange[1]
  )

  // Apply sorting
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'name':
        return a.name.localeCompare(b.name)
      case 'name-desc':
        return b.name.localeCompare(a.name)
      case 'price':
        return a.price - b.price
      case 'price-desc':
        return b.price - a.price
      case 'rating':
        return b.rating - a.rating
      default:
        return 0
    }
  })

  return filtered
})

const totalPages = computed(() =>
  Math.ceil(filteredProducts.value.length / props.pageSize)
)

const paginatedProducts = computed(() => {
  if (!props.pagination) return filteredProducts.value

  const start = (currentPage.value - 1) * props.pageSize
  const end = start + props.pageSize
  return filteredProducts.value.slice(start, end)
})

// Methods
const formatPrice = (price: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(price * 1000) // Assuming price is in thousands
}

const clearFilters = () => {
  filters.value = {
    category: null,
    priceRange: [0, 3000]
  }
}

const handleProductClick = (product: Product) => {
  emit('productClick', product)
}

const handleAddToCart = (product: Product) => {
  emit('addToCart', product)
}

const handleToggleFavorite = (product: Product) => {
  emit('toggleFavorite', product)
}

// Watch for filter changes to reset pagination
watch(filters, () => {
  currentPage.value = 1
}, { deep: true })
</script>
